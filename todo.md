# Flow Balance 开发流程 Todo List

## 项目设置与核心基础架构 (阶段零)

### 1. 项目初始化
- [x] **任务：初始化 Next.js 项目**
  - [x] 使用 `create-next-app` 创建新的 Next.js 项目（使用 App Router）
  - [x] 设置 Tailwind CSS 用于样式化
  - [x] 配置 TypeScript

### 2. 数据库设置
- [x] **任务：设置 Prisma 与 SQLite (本地开发)**
  - [x] 安装 Prisma CLI 和 Prisma Client
  - [x] 初始化 Prisma (`npx prisma init`)
  - [x] 为本地开发配置 `schema.prisma`，使用 SQLite provider
  - [x] 为生产环境配置 PostgreSQL provider，并使用环境变量

### 3. 数据模型定义
- [x] **任务：定义核心数据模型 (Prisma Schema)**
  - [x] `User` (用户) 模型
  - [x] `UserSettings` (用户设置, 与 User 一对一) 模型
  - [x] `Account` (账户, 属于 User) 模型
  - [x] `Category` (分类, 属于 User, 自引用实现树状结构) 模型
  - [x] `Transaction` (交易, 属于 User, 关联 Account, Category, Currency) 模型
  - [x] `Currency` (币种, 全局, 可预填充) 模型
  - [x] `Tag` (标签, 属于 User) 模型
  - [x] `TransactionTag` (交易标签关联表) 模型
  - [x] 生成初始迁移：`npx prisma migrate dev --name init`

### 4. 数据填充
- [x] **任务：设置模拟数据填充脚本**
  - [x] 创建 Prisma seed 脚本 (`prisma/seed.ts`)
  - [x] 包含常用币种 (USD, EUR, CNY, JPY)
  - [x] 包含 2-3 个用户，每个用户都有自己的一套分类、账户和交易

### 5. API 基础设施
- [x] **任务：实现 API 路由/Server Action 工具函数**
  - [x] 用户认证辅助函数 (JWT 验证)
  - [x] 获取当前登录用户的 ID
  - [x] 标准化 API 响应 (成功/错误)

---

## 零、认证与用户管理 (Authentication & User Management)

### 0.1 登录视图 (Login View)
- [x] **页面路由：** `/login`
- [x] **组件：`AuthLayout` (服务端组件)**
  - [x] 包裹所有认证页面的布局组件
  - [x] 显示产品 Logo/名称
- [x] **组件：`LoginPage` (服务端组件)**
  - [x] 使用 `AuthLayout`
  - [x] 包含 `LoginForm`
- [x] **组件：`LoginForm` (客户端组件)**
  - [x] 邮箱输入框 (`InputField`)
  - [x] 密码输入框 (`InputField`)
  - [x] "登录" 按钮 (`AuthButton`)
  - [x] "忘记密码？"链接
  - [x] "还没有账户？注册"链接
  - [x] 客户端基本校验 (邮箱格式、密码非空)
  - [x] 提交时调用 API 进行登录
  - [x] 显示来自 API 的错误信息
  - [x] 成功后重定向到 Dashboard
- [x] **API：`POST /api/auth/login`**
  - [x] 验证用户凭据 (哈希密码)
  - [x] 生成并返回会话令牌 (JWT)

### 0.2 注册视图 (Sign-up View)
- [x] **页面路由：** `/signup`
- [x] **组件：`SignupPage` (服务端组件)**
  - [x] 使用 `AuthLayout`
  - [x] 包含 `SignupForm`
- [x] **组件：`SignupForm` (客户端组件)**
  - [x] 邮箱输入框 (`InputField`)
  - [x] 密码输入框 (`InputField`)
  - [x] 确认密码输入框 (`InputField`)
  - [x] "注册" 按钮 (`AuthButton`)
  - [x] "已有账户？登录"链接
  - [x] 客户端校验 (邮箱格式、密码强度、两次密码是否一致)
  - [x] 提交时调用 API 进行注册
  - [x] 显示错误信息
  - [x] 成功后重定向到登录页
- [x] **API：`POST /api/auth/signup`**
  - [x] 检查邮箱是否存在
  - [x] 创建新 `User` (哈希密码)
  - [x] 为新用户创建默认的 `UserSettings`

### 0.3 忘记密码视图 (Forgot Password View)
- [x] **页面路由：** `/forgot-password`
- [x] **组件：`ForgotPasswordPage` (服务端组件)**
  - [x] 使用 `AuthLayout`
  - [x] 包含 `ForgotPasswordForm`
- [x] **组件：`ForgotPasswordForm` (客户端组件)**
  - [x] 提示文本
  - [x] 邮箱输入框 (`InputField`)
  - [x] "发送重置链接" 按钮 (`AuthButton`)
  - [x] "返回登录"链接
  - [x] 客户端邮箱格式校验
  - [x] 提交时调用 API
  - [x] 显示通用的成功/提示信息
- [x] **API：`POST /api/auth/request-password-reset`**
  - [x] 通过邮箱查找用户
  - [x] 生成唯一的、有时效性的重置令牌
  - [x] 存储令牌 (在 User 表中并记录过期时间)
  - [x] 发送包含重置链接的邮件 (目前可 mock 邮件发送)

### 0.4 重置密码视图 (Reset Password View)
- [x] **页面路由：** `/reset-password` (期望 URL 中带有 `token` 查询参数)
- [x] **组件：`ResetPasswordPage` (服务端组件)**
  - [x] 使用 `AuthLayout`
  - [x] 包含 `ResetPasswordForm`
  - [x] 从 URL 查询参数中读取令牌
- [x] **组件：`ResetPasswordForm` (客户端组件)**
  - [x] 提示文本
  - [x] 新密码输入框 (`InputField`)
  - [x] 确认新密码输入框 (`InputField`)
  - [x] "重置密码" 按钮 (`AuthButton`)
  - [x] 客户端校验 (密码强度、两次密码是否一致)
  - [x] 提交时调用 API 并携带令牌和新密码
  - [x] 显示成功/错误信息
  - [x] 成功后重定向到登录页
- [x] **API：`POST /api/auth/reset-password`**
  - [x] 验证令牌的有效性 (是否存在、未过期)
  - [x] 更新用户密码 (哈希处理)
  - [x] 使令牌失效

---

## 一、主界面布局 (Two-Pane Layout) - 用户登录后

### 1.1 应用布局
- [x] **组件：`AppLayout` (服务端组件)**
  - [x] 已认证用户的主布局
  - [x] 强制执行身份验证 (如果无有效会话，则重定向到 `/login`)
  - [x] 包含 `TopUserStatusBar`
  - [x] 左侧面板：`NavigationSidebar`
  - [x] 右侧面板：主内容区 (动态，作为 `children` 传入)
  - [x] 管理当前用户信息 (来自会话)

### 1.2 顶部用户状态栏
- [x] **组件：`TopUserStatusBar` (客户端组件)**
  - [x] 细长的顶部栏
  - [x] 产品 Logo/名称
  - [x] 右侧：用户邮箱/昵称，下拉箭头/用户头像
  - [x] 点击用户信息切换 `UserMenuDropdown` 的显示/隐藏

### 1.3 用户菜单下拉
- [x] **组件：`UserMenuDropdown` (客户端组件)**
  - [x] 包含"账户设置"链接 (指向 `/settings`)
  - [x] "登出"按钮的下拉菜单
  - [x] "登出"：调用 API 清除会话，然后重定向到 `/login`
- [x] **API：`POST /api/auth/logout`**
  - [x] 使会话令牌失效

---

## 二、导航侧边栏 (Navigation Sidebar) - 用户登录后

### 2.1 导航侧边栏主体
- [x] **组件：`NavigationSidebar` (客户端组件)**
  - [x] 固定宽度，内容溢出时可滚动
  - [x] 搜索词状态管理
  - [x] 树状项目的展开/折叠状态
  - [x] 当前选中项状态
  - [x] 获取用户的分类和账户数据

### 2.2 侧边栏子组件
- [x] **组件：`SidebarSearchBox`**
  - [x] 带占位符的输入框，清除图标
  - [x] 根据输入实时过滤 `CategoryAccountTree`

- [x] **组件：`SidebarDashboardLink`**
  - [x] "Dashboard" 文本，可选图标
  - [x] 点击后导航到 Dashboard 视图
  - [x] 选中时高亮

- [x] **组件：`CategoryAccountTree`**
  - [x] 渲染 `CategoryTreeItem` 和 `AccountTreeItem` 组件构成的树
  - [x] 处理获取分类和账户 (按 `user_id` 过滤)
  - [x] 管理父分类的展开/折叠
  - [x] 根据 `SidebarSearchBox` 的输入进行过滤

- [x] **组件：`CategoryTreeItem` (子分类递归使用)**
  - [x] 展开/折叠图标 (`▼`/`▶`)
  - [x] 分类名称
  - [x] 用于 `CategoryContextMenu` 的 `...` 图标
  - [x] 选中时高亮
  - [x] 点击名称/图标：切换展开状态，导航到"分类汇总视图"
  - [x] 点击 `...`：打开 `CategoryContextMenu`

- [x] **组件：`AccountTreeItem`**
  - [x] 图标 (`📄`)
  - [x] 账户名称
  - [x] 用于 `AccountContextMenu` 的 `...` 图标
  - [x] 选中时高亮
  - [x] 点击名称：导航到"账户详情视图"
  - [x] 点击 `...`：打开 `AccountContextMenu`

- [x] **组件：`CategoryContextMenu` / `AccountContextMenu`**
  - [x] 包含相关操作的弹出菜单
  - [x] 添加子分类、添加账户、重命名、删除、设置、移动
  - [x] 每个选项触发模态框或进行导航

### 2.3 侧边栏 API 端点
- [x] **分类 (Categories) API：**
  - [x] `GET /api/categories` (获取当前登录用户的所有分类)
  - [x] `POST /api/categories` (为用户创建新分类)
  - [x] `PUT /api/categories/{id}` (重命名分类)
  - [x] `DELETE /api/categories/{id}` (删除分类)

- [x] **账户 (Accounts) API：**
  - [x] `GET /api/accounts` (获取当前登录用户的所有账户)
  - [x] `POST /api/accounts` (为用户创建新账户)
  - [x] `PUT /api/accounts/{id}` (重命名账户)
  - [x] `DELETE /api/accounts/{id}` (删除账户)

---

## 三、主内容区 (Main Content Area) - 动态视图，用户登录后

### 3.1 Dashboard 视图
- [x] **页面路由：** `/dashboard` (或登录后的默认路由 `/`)
- [x] **组件：`DashboardView` (服务端组件)**
  - [x] 各种卡片/模块的布局
  - [x] 获取当前登录用户的聚合数据

- [x] **子组件：**
  - [x] `NetWorthCard`：以本位币显示净资产总额 (资产 - 负债)
  - [x] `RecentActivityCard`：近期收支摘要
  - [x] `AccountBalancesCard`：关键账户及其余额列表 (多币种)
  - [x] `QuickTransactionButton`："记一笔..."按钮，打开 `TransactionFormModal`
  - [x] `RecentTransactionsList`：显示最近 5-10 条交易

- [x] **API：`GET /api/dashboard/summary`**
  - [x] 获取并聚合当前用户的数据 (净资产、近期交易、账户余额)

### 3.2 分类汇总视图
- [x] **页面路由：** `/categories/[categoryId]`
- [x] **组件：`CategorySummaryView` (初始加载为服务端组件)**
  - [x] 标题 (分类名称)
  - [x] 汇总信息 (此分类下的总金额)
  - [x] 此分类下的子分类和账户列表
  - [x] 图表显示

- [x] **API：`GET /api/categories/[categoryId]/summary`**
  - [x] 获取分类详情、其直接子分类、账户以及当前用户的聚合金额

### 3.3 账户详情视图
- [x] **页面路由：** `/accounts/[accountId]`
- [x] **组件：`AccountDetailView` (初始加载为服务端组件)**
  - [x] 标题 (账户名称)
  - [x] 余额 (多币种)
  - [x] `[➕ 新增交易]` 按钮
  - [x] `TransactionList`

- [x] **子组件：**
  - [x] `AccountBalanceDisplay`：显示账户中每种币种的余额
  - [x] `TransactionList`：显示 `TransactionListItem` 组件
  - [x] `TransactionListItem`：显示单条交易的日期、描述、金额、分类，允许编辑/删除

- [x] **交互逻辑：**
  - [x] `[➕ 新增交易]` 按钮：打开 `TransactionFormModal`，并预填此账户

- [x] **API：**
  - [x] `GET /api/accounts/[accountId]/details`
  - [x] `GET /api/accounts/[accountId]/transactions`

### 3.4 交易录入/编辑表单
- [x] **组件：`TransactionFormModal` (客户端组件)**
  - [x] 包含 `TransactionForm` 的模态框
  - [x] 可见性状态管理
  - [x] 模式 (新增/编辑)
  - [x] 用于编辑的当前交易数据

- [x] **组件：`TransactionForm` (客户端组件)**
  - [x] 类型 (收入/支出/转账 - 单选/选择框)
  - [x] 日期 (`DatePicker`)
  - [x] 账户 (`AccountSelector` - 用户的账户列表)
  - [x] 金额 (数字输入框)
  - [x] 币种 (`CurrencySelector` - 来自全局 Currency 表)
  - [x] 分类 (`CategorySelector` - 用户的分类列表)
  - [x] 描述 (文本输入框)
  - [x] 标签 (`TagInput`)
  - [x] 备注 (文本域)
  - [x] `[保存]`、`[取消]` 按钮
  - [x] 客户端校验
  - [x] 保存时：调用 API 创建/更新交易
  - [x] 关闭模态框并刷新相关视图

- [x] **用于交易的 API 端点：**
  - [x] `POST /api/transactions` (为用户创建新交易)
  - [x] `PUT /api/transactions/{id}` (更新用户交易)
  - [x] `DELETE /api/transactions/{id}` (删除用户交易)

---

## 四、用户个人设置视图 (User Account Settings View)

- [x] **页面路由：** `/settings`
- [x] **组件：`UserSettingsPage` (布局为服务端组件)**
  - [x] 标签页界面或分块页面

### 4.1 个人资料设置
- [x] **组件：`ProfileSettingsForm` (客户端组件)**
  - [x] 昵称输入框
  - [ ] 头像上传功能
- [x] **API：`PUT /api/user/profile`**

### 4.2 安全设置
- [x] **组件：`ChangePasswordForm` (客户端组件)**
  - [x] 旧密码输入框
  - [x] 新密码输入框
  - [x] 确认新密码输入框
- [x] **API：`POST /api/user/change-password`**

### 4.3 偏好设置
- [x] **组件：`PreferencesForm` (客户端组件)**
  - [x] 本位币选择器 (`CurrencySelector`)
  - [x] 日期格式下拉列表
  - [ ] 默认交易类型单选按钮
- [x] **API：`PUT /api/user/settings`** (更新 `UserSettings` 表)

### 4.4 数据管理
- [x] **组件：`DataManagementSection` (客户端组件)**
  - [x] `[导出我的数据]` 按钮
  - [x] `[删除我的账户]` 按钮
  - [x] 导出：调用 API `GET /api/user/data/export` (触发下载)
  - [x] 删除：打开 `ConfirmationModal` (需要重新输入密码确认)
- [x] **API：**
  - [x] `GET /api/user/data/export`
  - [x] `DELETE /api/user/account`

---

## 五、通用模态框/对话框 (全局组件)

### 5.1 通用模态框组件
- [x] **组件：`Modal` (客户端组件)**
  - [x] 基础模态框组件
  - [x] Props：`isOpen`, `onClose`, `title`, `children`

### 5.2 确认对话框
- [x] **组件：`ConfirmationModal` (客户端组件)**
  - [x] 标题、消息、"确认"按钮、"取消"按钮
  - [x] Props：`isOpen`, `title`, `message`, `onConfirm`, `onCancel`

### 5.3 输入对话框
- [x] **组件：`InputDialog` (客户端组件)**
  - [x] 标题、消息、输入框、"保存"按钮、"取消"按钮
  - [x] Props：`isOpen`, `title`, `message`, `initialValue`, `onSubmit`, `onCancel`, `placeholder`

---

## 六、通用 UI 组件

### 6.1 表单组件
- [x] **组件：`InputField` (客户端组件)**
  - [x] 可复用的输入框组件
  - [x] Props：`type`, `name`, `label`, `placeholder`, `value`, `onChange`, `error`

- [x] **组件：`SelectField` (客户端组件)**
  - [x] 可复用的选择框组件
  - [x] Props：`name`, `label`, `value`, `onChange`, `options`, `error`

- [x] **组件：`TextAreaField` (客户端组件)**
  - [x] 可复用的文本域组件
  - [x] Props：`name`, `label`, `value`, `onChange`, `placeholder`, `error`

- [x] **组件：`AuthButton` (客户端组件)**
  - [x] 认证表单的可复用按钮组件
  - [x] Props：`label`, `onClick`, `isLoading`, `disabled`

---

## 七、高级功能 (Stage 4 - 未来实现)

### 7.1 图表和可视化
- [x] **组件：`CategoryChart` (客户端组件)**
  - [x] 使用 ECharts 实现分类支出图表
  - [x] 饼图显示分类占比

### 7.2 交易管理高级功能
- [x] **页面路由：** `/transactions`
- [x] **组件：`TransactionListView` (服务端组件)**
  - [x] 完整的交易列表页面
  - [x] 包含过滤器和统计信息

- [x] **组件：`TransactionFilters` (客户端组件)**
  - [x] 日期范围过滤
  - [x] 账户过滤
  - [x] 分类过滤
  - [x] 交易类型过滤

- [x] **组件：`TransactionStats` (客户端组件)**
  - [x] 交易统计信息显示
  - [x] 收入、支出、净值统计

### 7.3 多币种支持
- [ ] **汇率管理**
  - [ ] 汇率 API 集成
  - [ ] 汇率历史记录
  - [ ] 自动汇率更新

### 7.4 数据导出
- [ ] **导出功能**
  - [ ] CSV 格式导出
  - [ ] Excel 格式导出
  - [ ] PDF 报告生成

### 7.5 用户体验优化
- [ ] **响应式设计**
  - [ ] 移动端适配
  - [ ] 平板端适配

- [ ] **性能优化**
  - [ ] 数据分页
  - [ ] 虚拟滚动
  - [ ] 缓存策略

---

## 开发优先级说明

### 已完成 ✅
- 项目基础设施 (Next.js + Prisma + Tailwind)
- 数据模型定义和迁移 (包含密码重置字段)
- 认证系统 (登录、注册、完整的密码重置流程)
- 主界面布局 (AppLayout, NavigationSidebar, TopUserStatusBar)
- 导航侧边栏完整功能
- Dashboard 基础视图和数据聚合API
- 账户详情视图和相关API
- 分类汇总视图和相关API
- 交易表单和管理
- 基础 UI 组件库 (包含增强的InputField和SelectField)
- 交易列表和过滤功能
- 用户设置页面 (个人资料、安全设置、偏好设置、数据管理)
- 通用模态框组件 (Modal, ConfirmationModal, InputDialog)
- 数据导出功能
- 完整的用户账户删除功能
- **项目成功构建** ✅ (所有TypeScript类型错误已修复)
- **生产环境配置** ✅ (PostgreSQL支持、环境变量配置)
- **部署文档** ✅ (详细的部署指南和配置说明)

### 进行中 🚧
- 高级图表和可视化功能

### 待实现 📋
- 头像上传功能
- 默认交易类型设置
- 多币种汇率支持
- 响应式设计优化
- 性能优化 (数据分页、虚拟滚动、缓存策略)
- 邮件服务集成 (密码重置邮件发送)

### 开发建议
1. **✅ 核心 API 端点已完成**：所有基础 CRUD 操作和数据聚合API已实现
2. **✅ 用户设置功能已完成**：个人资料、安全设置、偏好设置、数据管理已实现
3. **🚧 增强数据可视化**：完善图表和统计功能 (部分已完成，需要进一步优化)
4. **📋 优化用户体验**：添加加载状态、错误处理、响应式设计
5. **📋 测试和部署**：编写测试用例，准备生产环境部署

### 下一步开发重点
1. **完善图表功能**：优化现有的ECharts图表，添加更多可视化选项
2. **响应式设计**：确保在移动设备和平板上的良好体验
3. **性能优化**：实现数据分页、虚拟滚动等性能优化
4. **多币种支持**：集成汇率API，实现多币种转换
5. **用户体验优化**：添加加载动画、错误边界、离线支持等