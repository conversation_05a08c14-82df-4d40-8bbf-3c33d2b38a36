{"name": "persional-balance-sheet", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.9.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "echarts": "^5.6.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}